<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1755242560,
  'data' => 
  array (
    'news_list' => 
    array (
      0 => 
      array (
        'id' => '30434',
        'catid' => '7',
        'title' => '测试文章427 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '669',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126651',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      1 => 
      array (
        'id' => '37483',
        'catid' => '8',
        'title' => '测试文章476 - 子栏目',
        'author' => '系统管理员',
        'click' => '937',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126627',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯的子栏目',
      ),
      2 => 
      array (
        'id' => '40746',
        'catid' => '7',
        'title' => '测试文章739 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '584',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126596',
        'description' => '这是第739篇测试文章的内容。所属栏目：泊头新闻的子栏目生成时间：2025-05-13 18:17:12这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。这是一段测试内容，用于填充文章内容。222222222',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      3 => 
      array (
        'id' => '1275',
        'catid' => '6',
        'title' => '测试文章268 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '530',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126426',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      4 => 
      array (
        'id' => '24853',
        'catid' => '7',
        'title' => '测试文章846 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '279',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126406',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
      5 => 
      array (
        'id' => '2057',
        'catid' => '4',
        'title' => '测试文章50 - 泊头新闻',
        'author' => '系统管理员',
        'click' => '3',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126368',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻',
      ),
      6 => 
      array (
        'id' => '8815',
        'catid' => '6',
        'title' => '测试文章808 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '710',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126235',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      7 => 
      array (
        'id' => '33228',
        'catid' => '6',
        'title' => '测试文章221 - 新闻资讯',
        'author' => '系统管理员',
        'click' => '309',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126065',
        'description' => '',
        'thumb' => '',
        'catname' => '新闻资讯',
      ),
      8 => 
      array (
        'id' => '38677',
        'catid' => '4',
        'title' => '测试文章670 - 泊头新闻',
        'author' => '系统管理员',
        'click' => '349',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747126035',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻',
      ),
      9 => 
      array (
        'id' => '3298',
        'catid' => '7',
        'title' => '测试文章291 - 泊头新闻的子栏目',
        'author' => '系统管理员',
        'click' => '492',
        'is_top' => '0',
        'is_recommend' => '0',
        'addtime' => '1747125997',
        'description' => '',
        'thumb' => '',
        'catname' => '泊头新闻的子栏目',
      ),
    ),
    'total_row' => 
    array (
      'total' => '43886',
    ),
  ),
);
