<?php if (!defined('IN_BTMPS')) { exit('Access Denied'); } ?>
INSERT INTO `settings` VALUES ('1', 'site_name', '泊头生活网', 'basic', '显示在浏览器标题和页面中的网站名称'), ('2', 'site_title', '分类信息网站 - 免费发布信息平台', 'basic', '显示在首页的完整标题'), ('3', 'site_keywords', '分类信息,免费发布,信息平台22222', 'basic', '用于SEO优化的网站关键词，多个关键词用逗号分隔'), ('4', 'site_description', '分类信息网站是一个免费发布信息的平台，提供房产、招聘、二手交易等各类信息服务。2222', 'basic', '用于SEO优化的网站描述'), ('5', 'site_copyright', 'Copyright © 2025 分类信息网站 All Rights Reserved
阿斯顿', 'basic', '显示在网站底部的版权信息'), ('6', 'site_icp', '冀ICP备12345678号', 'basic', '网站ICP备案号'), ('7', 'site_analytics', '', 'basic', '网站访问统计代码，如百度统计'), ('8', 'site_status', '1', 'basic', '是否开启网站'), ('9', 'site_close_reason', '网站维护中，请稍后再访问...0002222', 'basic', '网站关闭时显示的消息'), ('10', 'allow_register', '1', 'user', '是否允许新用户注册'), ('11', 'verify_register', '0', 'user', '是否需要邮箱或手机验证'), ('12', 'upload_image_size', '6', 'upload', '允许上传的图片最大尺寸，单位MB'), ('13', 'allowed_extensions', 'jpg,jpeg,png,gif', 'upload', '允许上传的文件扩展名，多个用逗号分隔'), ('14', 'post_expiry_days', '60', 'content', '发布信息的默认有效期（天）'), ('16', 'list_page_size', '30', 'content', '分类列表每页显示数量（默认20条）'), ('20', 'index_size', '100', 'content', '首页信息调用数量'), ('21', 'upload_image_count', '6', 'upload', '每次最多上传图片数量'), ('22', 'search_interval', '5', 'search', '搜索时间间隔，用户两次搜索之间的最小间隔时间（秒）'), ('23', 'search_cache_time', '600', 'search', '搜索结果缓存时间（秒），0表示不缓存'), ('24', 'search_max_results', '1000', 'search', '搜索结果最大数量限制'), ('25', 'search_keyword_min_length', '2', 'search', '搜索关键词最小长度'), ('26', 'search_keyword_max_length', '50', 'search', '搜索关键词最大长度'), ('27', 'search_enable_highlight', '1', 'search', '是否启用搜索结果关键词高亮，1表示启用，0表示禁用'), ('28', 'mobile_pagination_mode', 'infinite', 'content', '移动端分页模式：pagination=传统分页，loadmore=点击加载更多，infinite=滚动无限加载'), ('29', 'cache_enable', '1', 'cache', '是否启用缓存功能，1表示启用，0表示禁用'), ('30', 'cache_index', '3600', 'cache', '首页缓存时间（秒），0表示不缓存'), ('31', 'cache_list', '1800', 'cache', '列表页缓存时间（秒），0表示不缓存'), ('32', 'cache_post', '1800', 'cache', '详情页缓存时间（秒），0表示不缓存'), ('33', 'cache_category', '1720000000', 'cache', '分类页缓存时间（秒），0表示不缓存'), ('34', 'cache_region', '1720000000', 'cache', '地区页缓存时间（秒），0表示不缓存'), ('35', 'cache_search', '60000', 'cache', '搜索结果缓存时间（秒），0表示不缓存'), ('36', 'cache_data', '36000', 'cache', '数据缓存时间（秒），0表示不缓存'), ('37', 'cache_compress', '1', 'cache', '是否启用缓存压缩，1表示启用，0表示禁用'), ('38', 'clear_cache_time', '24', 'cache', '缓存清理周期（小时），自动清理过期缓存'), ('39', 'warmup_enable', '1', 'warmup', '是否启用自动缓存预热'), ('40', 'warmup_interval', '1', 'warmup', '缓存预热间隔（小时）'), ('41', 'warmup_popular_posts_limit', '300', 'warmup', '预热热门信息数量限制'), ('42', 'warmup_category_limit', '0', 'warmup', '预热分类数量限制（0为不限制）'), ('43', 'warmup_region_limit', '0', 'warmup', '预热地区数量限制（0为不限制）'), ('44', 'warmup_clean_expired', '1', 'warmup', '是否清理过期缓存'), ('45', 'cache_news_home', '1800', 'news_cache', '新闻首页缓存时间（秒），0表示不缓存，为空则使用首页缓存时间'), ('46', 'cache_news_list', '1800', 'news_cache', '新闻列表页缓存时间（秒），0表示不缓存，为空则使用列表页缓存时间'), ('47', 'cache_news_detail', '86400', 'news_cache', '新闻详情页缓存时间（秒），0表示不缓存，为空则使用详情页缓存时间'), ('48', 'cache_news_category', '21600', 'news_cache', '新闻分类缓存时间（秒），0表示不缓存，为空则使用分类缓存时间'), ('49', 'cache_news_hot', '7200', 'news_cache', '热门新闻缓存时间（秒），0表示不缓存'), ('50', 'hot_posts_count', '10', 'content', '首页热门信息显示数量'), ('51', 'news_index_count', '10', 'content', '首页资讯索引显示数量'), ('52', 'mobile_daily_post_limit', '2', 'content', '每个手机号码每天最多可发布的信息数量');
