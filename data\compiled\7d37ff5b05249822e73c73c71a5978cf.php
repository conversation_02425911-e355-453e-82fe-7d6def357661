<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>编辑信息 - <?php echo $post['title']; ?></title>
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/post.css?v=20250408">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/image-compress.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
    <script src="/template/pc/js/jquery.min.js"></script>
    <style>
    /* 表单验证样式 */
    .inputTip {
        margin-top: 5px;
        font-size: 13px;
        color: #888;
    }
    .inputTip.focus {
        color: #1890ff;
    }
    .inputTip.success {
        color: #52c41a;
    }
    .inputTip.error {
        color: #f5222d;
    }
    .error-input {
        border-color: #f5222d !important;
    }
    .has-success .form-control {
        border-color: #52c41a !important;
    }
    </style>
</head>
<body>
    <!-- 顶部 -->
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

	<?php if($site_analytics): ?>
	<!-- 网站统计代码 -->
	<?php echo $site_analytics ?? ""; ?>
	<?php endif; ?>


    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="content-wrap">
            <!-- 左侧表单区域 -->
            <div class="left-column">
                <div class="pd10">
                    <div class="yui-h-title">
                        <h3>编辑信息</h3><span></span>
                    </div>
                </div>
                <div class="posting-notice">
                    <h4 class="notice-title">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>编辑须知</span>
                    </h4>
                    <div class="notice-content">
                        <p>修改后的信息需要重新审核</p>
                        <p>请确保修改后的信息符合网站规定</p>
                        <p>后台审核信息时可能会对部分字词句进行调整</p>
                        <p class="highlight">如有图片，请上传自己拍摄的图片，盗图或使用字体侵权风险自担</p>
                        <p>招聘不得限定男女性别,涉嫌违法一律不予通过</p>
                        <p>如继续修改提交信息,视为您已知晓并同意该协议</p>
                        <p>请认真阅读本页"编辑须知"，以及<a href="/help/statement.html" target="_blank" class="agreement-link">网站声明</a>、<a href="/help/review.html" target="_blank" class="agreement-link">审核条例</a>文档内容</p>
                        <p class="highlight">点击"保存修改"即代表你已阅读并同意我们的<a href="/help/service.html" target="_blank" class="agreement-link">服务条款</a>!</p>
                    </div>
                </div>
                <form id="edit-form" action="/post.php?action=edit&id=<?php echo $post['id']; ?>" method="post" enctype="multipart/form-data" novalidate>
                    <input type="hidden" name="id" value="<?php echo $post['id']; ?>">
                    <input type="hidden" name="password" value="<?php echo isset($password) ? $password : ''; ?>">
                    
                    <!-- CSRF令牌 -->
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <?php if(null !== ($error_message ?? null) && !empty($error_message)): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-circle"></i><?php echo $error_message ?? ""; ?>
                    </div>
                    <?php endif; ?>

                    <div class="form-panel">
                        <!-- 当前栏目 -->
                        <div class="category-row">
                            <label class="form-label">当前栏目</label>
                            <div class="category-content">
                                <?php echo $post['category_name']; ?>
                                <a href="/post.php" class="change-link">[重选栏目]</a>
                            </div>
                        </div>

                        <!-- 选择地区 -->
                        <div class="form-group required">
                            <label for="region_id" class="form-label">所在地区</label>
                            <div class="form-right">
                                <div class="custom-select">
                                    <input type="hidden" name="region_id" id="region_id" value="<?php echo $post['region_id']; ?>" required>
                                    <div class="select-trigger"><?php echo $post['region_name']; ?></div>
                                    <div class="select-dropdown">
                                        <?php foreach ($regions as $province): ?>
                                        <div class="select-group">
                                            <div class="group-label"><?php echo $province['name']; ?></div>
                                            <div class="group-options">
                                                <?php foreach ($province['children'] as $city): ?>
                                                <div class="select-option" data-value="<?php echo $city['id']; ?>" <?php echo ($post['region_id'] == $city['id']) ? 'class="selected"' : ''; ?>><?php echo $city['name']; ?></div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle">
                                    <i class="fas fa-info-circle"></i>
                                    <i class="fas fa-check-circle" style="display: none;"></i>
                                    <span>请选择您所在的地区</span>
                                </span>
                            </div>
                        </div>
                        
                        <!-- 信息有效期 -->
                        <div class="form-group required">
                            <label for="expire_days" class="form-label">有效期</label>
                            <div class="form-right">
                                <div class="custom-select">
                                    <input type="hidden" name="expire_days" id="expire_days" value="<?php echo $post['expire_days']; ?>" required>
                                    <div class="select-trigger"><?php echo $post['expire_days']; ?>天</div>
                                    <div class="select-dropdown">
                                        <div class="select-option" data-value="7" <?php echo ($post['expire_days'] == 7) ? 'class="selected"' : ''; ?>>7天</div>
                                        <div class="select-option" data-value="15" <?php echo ($post['expire_days'] == 15) ? 'class="selected"' : ''; ?>>15天</div>
                                        <div class="select-option" data-value="30" <?php echo ($post['expire_days'] == 30) ? 'class="selected"' : ''; ?>>30天</div>
                                        <div class="select-option" data-value="60" <?php echo ($post['expire_days'] == 60) ? 'class="selected"' : ''; ?>>60天</div>
                                        <div class="select-option" data-value="90" <?php echo ($post['expire_days'] == 90) ? 'class="selected"' : ''; ?>>90天</div>
                                    </div>
                                </div>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle">
                                    <i class="fas fa-info-circle"></i>
                                    <i class="fas fa-check-circle" style="display: none;"></i>
                                    <span>请选择信息的有效期限</span>
                                </span>
                            </div>
                        </div>
                        
                        <!-- 标题 -->
                        <div class="form-group required">
                            <label for="title" class="form-label">标题</label>
                            <div class="form-right">
                                <input type="text" name="title" id="title" class="form-input" placeholder="请输入标题" value="<?php echo $post['title']; ?>" required>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle"><i class="fas fa-info-circle"></i><span>请输入简洁明了的标题</span></span>
                            </div>
                        </div>
                        
                        <!-- 内容 -->
                        <div class="form-group required">
                            <label for="content" class="form-label">详细内容</label>
                            <div class="form-right">
                                <textarea name="content" id="content" class="form-textarea" placeholder="请输入详细内容" required><?php echo $post['content']; ?></textarea>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle"><i class="fas fa-info-circle"></i><span>请详细描述信息内容，越详细越好</span></span>
                            </div>
                        </div>

                        <!-- 图片上传 -->
                        <div class="form-group">
                            <label class="form-label">上传图片</label>
                            <div class="form-right">
                                <div class="image-upload">
                                    <div class="upload-area">
                                        <input type="file" id="image_uploads" name="images[]" accept="image/*" multiple class="hidden-input">
                                        <label for="image_uploads" class="upload-btn">
                                            <i class="fas fa-image"></i>
                                            <span>添加图片</span>
                                        </label>
                                        <div class="image-hint">最多可上传<?php echo (isset($upload_config['max_count'])) ? $upload_config['max_count'] : ""; ?>张（选填）<br><span class="warning">请上传非侵权图片和字体图，否则后果自负</span></div>
                                    </div>
                                    <div class="image-previews" id="image-previews">
                                        <?php if (!empty($post_images)): ?>
                                            <?php foreach ($post_images as $image): ?>
                                            <div class="image-item">
                                                <img src="<?php echo isset($image['url']) ? $image['url'] : $image['thumb_path']; ?>" alt="已上传图片">
                                                <span class="remove-image" data-id="<?php echo $image['id']; ?>">×</span>
                                                <input type="hidden" name="existing_images[]" value="<?php echo $image['id']; ?>">
                                            </div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 联系人 -->
                        <div class="form-group required">
                            <label for="contact_name" class="form-label">联系人</label>
                            <div class="form-right">
                                <input type="text" name="contact_name" id="contact_name" class="form-input" placeholder="请输入联系人姓名" value="<?php echo $post['contact_name']; ?>" required>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle"><i class="fas fa-info-circle"></i><span>请填写能够联系到您的真实姓名</span></span>
                            </div>
                        </div>
                        
                        <!-- 联系电话 -->
                        <div class="form-group required">
                            <label for="contact_mobile" class="form-label">联系电话</label>
                            <div class="form-right">
                                <input type="tel" name="contact_mobile" id="contact_mobile" class="form-input" placeholder="请输入手机号码" value="<?php echo $post['contact_mobile']; ?>" required>
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle"><i class="fas fa-info-circle"></i><span>请填写您的11位手机号码</span></span>
                            </div>
                        </div>

                        <!-- 详细地址 -->
                        <div class="form-group">
                            <label for="contact_address" class="form-label">详细地址</label>
                            <div class="form-right">
                                <input type="text" name="contact_address" id="contact_address" class="form-input" placeholder="请输入详细地址（选填）" value="<?php echo $post['contact_address']; ?>">
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle" data-tip-text="请填写详细的地址信息（选填）">
                                    <i class="fas fa-info-circle"></i>
                                    <span class="tip-text">请填写详细的地址信息（选填）</span>
                                </span>
                            </div>
                        </div>

                        <!-- 微信号 -->
                        <div class="form-group">
                            <label for="contact_weixin" class="form-label">微信号</label>
                            <div class="form-right weixin-group">
                                <input type="text" name="contact_weixin" id="contact_weixin" class="form-input" placeholder="微信号（选填）" value="<?php echo $post['contact_weixin']; ?>">
                                <span class="inputTip" data-success-icon="fa-check-circle" data-default-icon="fa-info-circle" data-tip-text="请填写您的微信号（选填）">
                                    <i class="fas fa-info-circle"></i>
                                    <span class="tip-text">请填写您的微信号（选填）</span>
                                </span>
                                <label class="weixin-checkbox">
                                    <input type="checkbox" id="weixin_same"> 与手机相同
                                </label>
                            </div>
                        </div>
                        
                        <!-- 提交按钮 -->
                        <div class="submit-group">
                            <button type="submit" class="submit-button" id="submit-btn">保存修改</button>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 右侧提示区域 -->
            <div class="right-column">
                <div class="side-panel">
                    <div class="panel-title">编辑流程</div>
                    <div class="panel-content">
                        <div class="process-list">
                            <div class="process-item">
                                <span class="process-num">1</span>
                                <span class="process-text">修改需要更新的信息</span>
                            </div>
                            <div class="process-item">
                                <span class="process-num">2</span>
                                <span class="process-text">提交并等待审核</span>
                            </div>
                            <div class="process-item">
                                <span class="process-num">3</span>
                                <span class="process-text">信息更新成功</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="side-panel">
                    <div class="panel-title">温馨提示</div>
                    <div class="panel-content">
                        <ul class="tips-list">
                            <li>修改后的信息需要重新审核</li>
                            <li>请确保修改后的信息符合网站规定</li>
                            <li>信息审核时间为工作时间内1-2小时</li>
                            <li>如有疑问可联系客服咨询</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <div class="loading-text">正在提交中...</div>
        </div>
    </div>
    
    <!-- 提交成功提示 -->
    <div class="success-overlay" id="success-overlay">
        <div class="success-message">
            <i class="fas fa-check-circle"></i>
            <div class="success-text">操作成功！</div>
            <div class="success-subtext">您的信息已成功更新</div>
            <button class="success-btn" id="success-btn">查看信息</button>
        </div>
    </div>

    <!-- 底部 -->
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <!-- 友情链接区域 -->
          
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright"><?php if($site_copyright): ?><?php echo $site_copyright ?? ""; ?><?php else: ?>Copyright © 2024 分类信息网站 All Rights Reserved<?php endif; ?></p>
            <?php if($site_icp): ?><p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow"><?php echo $site_icp ?? ""; ?></a></p><?php endif; ?>
        </div>
    </div>
</div>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="/static/js/image-compress.js"></script>
    <script>
    // 简化的图片压缩功能（内嵌版本）
    function SimpleImageCompressor() {
        this.compressFile = function(file) {
            return new Promise((resolve, reject) => {
                if (!file.type.match('image.*')) {
                    reject(new Error('不支持的文件类型'));
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 计算新尺寸
                            let { width, height } = img;
                            const maxWidth = 1920;
                            const maxHeight = 1080;

                            if (width > maxWidth || height > maxHeight) {
                                const ratio = Math.min(maxWidth / width, maxHeight / height);
                                width *= ratio;
                                height *= ratio;
                            }

                            canvas.width = width;
                            canvas.height = height;

                            // 绘制图片
                            ctx.drawImage(img, 0, 0, width, height);

                            // 转换为Blob
                            canvas.toBlob(function(blob) {
                                const compressedFile = new File([blob], file.name, {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                });

                                resolve({
                                    file: compressedFile,
                                    originalSize: file.size,
                                    compressedSize: blob.size,
                                    compressionRatio: (blob.size / file.size).toFixed(2),
                                    quality: '0.80',
                                    dimensions: { width: Math.round(width), height: Math.round(height) }
                                });
                            }, 'image/jpeg', 0.8);

                        } catch (error) {
                            reject(error);
                        }
                    };
                    img.onerror = () => reject(new Error('图片加载失败'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        };
    }
    </script>

    <script src="/template/pc/js/post-ajax.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 表单验证
        const form = document.querySelector('form');
        const loadingOverlay = document.getElementById('loading-overlay');
        const successOverlay = document.getElementById('success-overlay');
        const successBtn = document.getElementById('success-btn');
        
        // 编辑须知折叠功能
        const noticeTitle = document.querySelector('.notice-title');
        const noticeContent = document.querySelector('.notice-content');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        noticeTitle.addEventListener('click', function() {
            noticeContent.classList.toggle('collapsed');
            toggleIcon.classList.toggle('rotate');
        });
        
        // jQuery表单验证和提交
        $(document).ready(function() {
            // 自定义验证方法 - 手机号
            $.validator.addMethod("isMobile", function(value, element) {
                var mobile = /^1\d{10}$/;
                return this.optional(element) || (mobile.test(value));
            }, "请输入正确的手机号码");
            
            // 验证配置
            $("#edit-form").validate({
                rules: {
                    title: {
                        required: true,
                        minlength: 5,
                        maxlength: 50
                    },
                    content: {
                        required: true,
                        minlength: 10,
                        maxlength: 5000
                    },
                    region_id: "required",
                    expire_days: "required",
                    contact_name: "required",
                    contact_mobile: {
                        required: true,
                        isMobile: true
                    }
                },
                messages: {
                    title: {
                        required: "请输入标题",
                        minlength: "标题至少5个字符",
                        maxlength: "标题最多50个字符"
                    },
                    content: {
                        required: "请输入内容",
                        minlength: "内容至少10个字符",
                        maxlength: "内容最多5000个字符"
                    },
                    region_id: "请选择所在地区",
                    expire_days: "请选择有效期",
                    contact_name: "请输入联系人姓名",
                    contact_mobile: {
                        required: "请输入手机号码",
                        isMobile: "请输入正确的手机号码"
                    }
                },
                errorPlacement: function(error, element) {
                    var $tip = element.siblings(".inputTip");
                    $tip.removeClass('focus success').addClass('error')
                        .html('<i class="fas fa-exclamation-circle"></i> <span class="tip-text">' + error.text() + '</span>');
                },
                highlight: function(element, errorClass, validClass) {
                    $(element).addClass(errorClass).removeClass(validClass);
                },
                unhighlight: function(element, errorClass, validClass) {
                    var $element = $(element);
                    $element.removeClass(errorClass).addClass(validClass);
                    
                    // 如果有值，显示成功状态
                    if ($element.val()) {
                        var $tip = $element.siblings(".inputTip");
                        var originalText = $tip.data('originalText');
                        $tip.removeClass('error focus').addClass('success')
                            .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    } else {
                        // 如果没有值，恢复默认状态
                        var $tip = $element.siblings(".inputTip");
                        var originalText = $tip.data('originalText');
                        $tip.removeClass('error focus success')
                            .html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    }
                },
                submitHandler: function(form) {
                    // 显示加载中遮罩
                    $('#loading-overlay').css('display', 'flex').fadeIn();
                    
                    // 使用FormData获取表单数据
                    var formData = new FormData(form[0]);
                    
                    // 添加已有图片ID
                    var existingImages = [];
                    $('.image-item input[name="existing_images[]"]').each(function() {
                        existingImages.push($(this).val());
                    });
                    formData.append('existing_images', JSON.stringify(existingImages));
                    
                    // 添加submit字段，确保服务器端能识别表单提交
                    formData.append('submit', '1');
                    
                    // 添加ajax标记
                    formData.append('ajax', '1');
                    
                    // AJAX提交表单
                    $.ajax({
                        url: form.attr('action'),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        complete: function(xhr, status) {
                            // 删除调试信息，无需记录请求状态
                        },
                        success: function(response) {
                            // 隐藏加载遮罩
                            $('#loading-overlay').fadeOut();
                            
                            // 尝试解析响应（如果它还不是对象）
                            var responseData = response;
                            if (typeof response === 'string') {
                                try {
                                    responseData = JSON.parse(response);
                                } catch (e) {
                                    
                                    // 如果响应中包含成功信息，视为成功
                                    if (response.indexOf('成功') > -1) {
                                        $('#success-overlay').css('display', 'flex').fadeIn();
                                        return;
                                    } else {
                                        alert("保存失败: 服务器返回非JSON格式数据");
                                        return;
                                    }
                                }
                            }
                            
                            // 处理JSON响应
                            if (responseData && responseData.success) {
                                // 显示成功提示
                                // 重置成功弹窗的文本，确保显示正确的成功消息
                                $('.success-text').text('操作成功！');
                                $('.success-subtext').text('您的信息已成功更新');
                                $('#success-btn').text('查看信息');
                                
                                $('#success-overlay').css('display', 'flex').fadeIn();
                                
                                // 表单提交成功，为成功按钮绑定跳转到详情页的事件
                                $('#success-btn').off('click').on('click', function() {
                                    if (responseData.detail_url) {
                                        window.location.href = responseData.detail_url;
                                    } else {
                                        window.location.reload();
                                    }
                                });
                            } else {
                                // 显示错误信息
                                var message = responseData && responseData.message ? responseData.message : '保存失败，请稍后再试';
                                alert(message);
                            }
                        },
                        error: function(xhr, status, error) {
                            // 隐藏加载遮罩
                            $('#loading-overlay').fadeOut();
                            
                            // 尝试解析错误响应
                            var errorMessage = '保存失败，请稍后再试 (错误: ' + error + ')';
                            try {
                                if (xhr.responseText) {
                                    var response = JSON.parse(xhr.responseText);
                                    if (response.message) {
                                        errorMessage = response.message;
                                    }
                                }
                            } catch (e) {
                                errorMessage += ' (无法解析响应)';
                            }
                            
                            alert(errorMessage);
                        }
                    });
                    
                    return false;
                }
            });
            
            // 初始化时为已有值且非必填的字段显示成功图标
            $("#edit-form input:not([required]), #edit-form select:not([required]), #edit-form textarea:not([required])").each(function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.find('.tip-text').text();
                $tip.data('originalText', originalText);
                
                if ($this.val()) {
                    $this.addClass("valid");
                    $tip.addClass("success")
                        .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                } else {
                    $tip.html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                }
            });
            
            // 初始化时对已有值的必填字段进行验证
            $("#edit-form input[required], #edit-form select[required], #edit-form textarea[required]").each(function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.find('.tip-text').text();
                $tip.data('originalText', originalText);
                
                if ($this.val()) {
                    // 触发验证
                    if ($this.valid()) {
                        $tip.addClass("success")
                            .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    }
                } else {
                    $tip.html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                }
            });
            
            // 添加输入框焦点效果
            $("#edit-form input, #edit-form textarea, #edit-form select").on('focus', function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.data('originalText') || $tip.find('.tip-text').text();
                
                // 如果已经是成功状态，则不改变图标和状态
                if ($tip.hasClass('success')) {
                    return;
                }
                
                // 如果不是错误状态，则更新为焦点状态
                if (!$tip.hasClass('error')) {
                    $tip.data('originalText', originalText);
                    $tip.removeClass('success').addClass('focus')
                        .html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                }
            });
            
            // 输入框失去焦点效果
            $("#edit-form input, #edit-form textarea, #edit-form select").on('blur', function() {
                var $this = $(this);
                var $tip = $this.siblings(".inputTip");
                var originalText = $tip.data('originalText') || $tip.find('.tip-text').text();
                
                // 如果已经是成功状态，则不改变图标和状态
                if ($tip.hasClass('success')) {
                    return;
                }
                
                // 只处理没有错误的情况
                if (!$tip.hasClass('error')) {
                    // 如果有值且验证通过，显示成功状态
                    if ($this.val() && !$this.hasClass('error')) {
                        $tip.removeClass('focus').addClass('success')
                            .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    } else {
                        // 恢复默认状态，确保显示文本
                        $tip.removeClass('focus success')
                            .html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + originalText + '</span>');
                    }
                }
            });
        });
        
        // 图片上传预览和压缩（编辑页面）
        const input = document.getElementById('image_uploads');
        const preview = document.getElementById('image-previews');

        // 初始化图片压缩器
        let compressor;

        if (typeof ImageCompressor !== 'undefined') {
            compressor = new ImageCompressor({
                maxWidth: 1920,
                maxHeight: 1080,
                quality: 0.8,
                targetSize: <?php echo (isset($upload_config['max_size'])) ? $upload_config['max_size'] : ""; ?> * 1024 * 1024,
                maxSize: 8 * 1024 * 1024,
                debug: false
            });
        } else {
            compressor = new SimpleImageCompressor();
        }

        // 创建进度提示（如果不存在）
        if (!document.getElementById('upload-progress')) {
            const progressHtml = `
                <div id="upload-progress" style="display: none; position: fixed; top: 50%; left: 50%;
                     transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white;
                     padding: 20px; border-radius: 8px; z-index: 9999; text-align: center;">
                    <div style="margin-bottom: 10px;">正在处理图片...</div>
                    <div style="width: 300px; height: 6px; background: #333; border-radius: 3px; overflow: hidden;">
                        <div id="progress-bar" style="width: 0%; height: 100%; background: #007bff;
                             border-radius: 3px; transition: width 0.3s;"></div>
                    </div>
                    <div id="progress-text" style="margin-top: 10px; font-size: 12px;">准备中...</div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', progressHtml);
        }

        // 更新进度函数
        function updateEditProgress(percentage, text) {
            const progressEl = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            if (progressEl) progressEl.style.display = 'block';
            if (progressBar) progressBar.style.width = percentage + '%';
            if (progressText && text) progressText.textContent = text;

            if (percentage >= 100) {
                setTimeout(() => {
                    if (progressEl) progressEl.style.display = 'none';
                }, 500);
            }
        }

        input.addEventListener('change', async function() {
            const files = Array.from(this.files);
            var maxCount = <?php echo (isset($upload_config['max_count'])) ? $upload_config['max_count'] : ""; ?>;

            // 计算已有图片数量（有data-id属性的是已有图片）
            var existingCount = document.querySelectorAll('.image-item .remove-image[data-id]').length;

            // 检查总数量是否超过限制
            if (existingCount + files.length > maxCount) {
                alert('图片总数不能超过' + maxCount + '张，当前已有' + existingCount + '张图片，最多还能上传' + (maxCount - existingCount) + '张');
                this.value = '';
                return;
            }

            if (files.length === 0) return;

            try {
                updateEditProgress(0, '开始处理新图片...');

                // 处理每个文件
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];

                    if (!file.type.match('image.*')) {
                        console.warn('跳过非图片文件:', file.name);
                        continue;
                    }

                    const progressPercent = Math.round(((i + 1) / files.length) * 100);
                    updateEditProgress(progressPercent, `正在处理第 ${i + 1} 张新图片...`);

                    try {
                        // 压缩图片
                        const result = await compressor.compressFile(file);

                        // 创建预览
                        const div = document.createElement('div');
                        div.className = 'image-item';
                        div.dataset.index = i;

                        const img = document.createElement('img');
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            img.src = e.target.result;
                        };
                        reader.readAsDataURL(result.file);

                        const removeBtn = document.createElement('span');
                        removeBtn.className = 'remove-image';
                        removeBtn.textContent = '×';
                        removeBtn.onclick = () => div.remove();

                        // 添加压缩信息提示
                        const infoBtn = document.createElement('span');
                        infoBtn.className = 'image-info';
                        infoBtn.innerHTML = 'ℹ';
                        infoBtn.style.cssText = `
                            position: absolute; top: 5px; left: 5px;
                            background: rgba(0,0,0,0.7); color: white;
                            border-radius: 50%; width: 20px; height: 20px;
                            text-align: center; line-height: 20px;
                            font-size: 12px; cursor: help;
                        `;

                        const originalSize = (file.size / 1024 / 1024).toFixed(2);
                        const compressedSize = (result.compressedSize / 1024 / 1024).toFixed(2);
                        const ratio = result.compressionRatio;

                        infoBtn.title = `原始大小: ${originalSize}MB\n压缩后: ${compressedSize}MB\n压缩比: ${ratio}`;

                        div.appendChild(img);
                        div.appendChild(removeBtn);
                        div.appendChild(infoBtn);
                        preview.appendChild(div);

                    } catch (error) {
                        console.error('图片压缩失败:', error);
                        alert(`图片 "${file.name}" 处理失败: ${error.message}`);
                    }
                }

                updateEditProgress(100, '新图片处理完成');

            } catch (error) {
                console.error('图片处理出错:', error);
                alert('图片处理出错: ' + error.message);
                updateEditProgress(0, '');
            }
        });
        
        // 删除图片处理
        $('.remove-image[data-id]').on('click', function() {
            const imageId = $(this).data('id');
            const imageItem = $(this).closest('.image-item');
            
            if(!confirm('确定要删除这张图片吗？')) {
                return false;
            }
            
            // 显示加载遮罩
            $('#loading-overlay').css('display', 'flex').fadeIn();
            
            // 请求删除图片
            $.ajax({
                url: '/post.php?action=delete_image',
                type: 'POST',
                data: {
                    id: imageId,
                    post_id: <?php echo intval($post['id']); ?>,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                success: function(response) {
                    $('#loading-overlay').fadeOut();
                    
                    if(response.success) {
                        // 从DOM中移除图片
                        imageItem.remove();
                        
                        // 显示成功提示而不是alert
                        $('.success-text').text('图片删除成功！');
                        $('.success-subtext').text('您可以继续编辑其他内容');
                        
                        // 为删除图片的成功消息配置按钮行为，只关闭对话框
                        $('#success-btn').text('继续编辑').off('click').on('click', function() {
                            $('#success-overlay').fadeOut();
                        });
                        
                        $('#success-overlay').css('display', 'flex').fadeIn();
                    } else {
                        alert(response.message || '删除图片失败，请重试');
                    }
                },
                error: function() {
                    $('#loading-overlay').fadeOut();
                    alert('删除图片失败，请稍后重试');
                }
            });
            
            return false;
        });
        
        // 微信号与手机号同步
        var mobileInput = document.getElementById('contact_mobile');
        var wechatInput = document.getElementById('contact_weixin');
        var wechatSame = document.getElementById('weixin_same');
        
        wechatSame.addEventListener('change', function() {
            if (this.checked) {
                wechatInput.value = mobileInput.value;
                wechatInput.disabled = true;
            } else {
                wechatInput.disabled = false;
            }
        });
        
        mobileInput.addEventListener('input', function() {
            if (wechatSame.checked) {
                wechatInput.value = this.value;
            }
        });
        
        // 自定义下拉框处理
        const customSelects = document.querySelectorAll('.custom-select');
        
        customSelects.forEach(select => {
            const trigger = select.querySelector('.select-trigger');
            const dropdown = select.querySelector('.select-dropdown');
            const options = select.querySelectorAll('.select-option');
            const input = select.querySelector('input[type="hidden"]');
            const tip = select.nextElementSibling;
            const infoIcon = tip.querySelector('.fa-info-circle');
            const successIcon = tip.querySelector('.fa-check-circle');
            
            // 点击触发器显示/隐藏下拉框
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                customSelects.forEach(s => {
                    if (s !== select) {
                        s.classList.remove('active');
                        s.classList.remove('error');
                    }
                });
                select.classList.toggle('active');
                
                // 显示焦点状态提示
                if (select.classList.contains('active')) {
                    select.classList.remove('valid');
                    select.classList.remove('error');
                    // 显示默认图标
                    infoIcon.style.display = 'inline-block';
                    successIcon.style.display = 'none';
                }
            });
            
            // 点击选项
            options.forEach(option => {
                option.addEventListener('click', () => {
                    const value = option.getAttribute('data-value');
                    const text = option.textContent;
                    
                    // 更新隐藏输入值和显示文本
                    input.value = value;
                    trigger.textContent = text;
                    
                    // 更新选中状态
                    options.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    
                    // 关闭下拉框
                    select.classList.remove('active');
                    
                    // 更新验证状态
                    select.classList.add('valid');
                    select.classList.remove('error');
                    
                    // 切换图标
                    infoIcon.style.display = 'none';
                    successIcon.style.display = 'inline-block';
                    
                    // 触发 change 事件
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                    
                    // 验证表单
                    $(input).valid();
                });
            });
        });
        
        // 点击其他地方关闭所有下拉框
        document.addEventListener('click', () => {
            customSelects.forEach(select => {
                select.classList.remove('active');
                // 如果没有选择值，显示错误状态
                const input = select.querySelector('input[type="hidden"]');
                const tip = select.nextElementSibling;
                const infoIcon = tip.querySelector('.fa-info-circle');
                const successIcon = tip.querySelector('.fa-check-circle');
                
                if (input.hasAttribute('required') && !input.value) {
                    select.classList.add('error');
                    select.classList.remove('valid');
                    // 显示默认图标
                    infoIcon.style.display = 'inline-block';
                    successIcon.style.display = 'none';
                }
            });
        });
        
        // 阻止下拉框内部点击事件冒泡
        document.querySelectorAll('.select-dropdown').forEach(dropdown => {
            dropdown.addEventListener('click', (e) => e.stopPropagation());
        });

        // 设置默认选中值（如果有）
        customSelects.forEach(select => {
            const input = select.querySelector('input[type="hidden"]');
            const options = select.querySelectorAll('.select-option');
            const trigger = select.querySelector('.select-trigger');
            const tip = select.nextElementSibling;
            const infoIcon = tip.querySelector('.fa-info-circle');
            const successIcon = tip.querySelector('.fa-check-circle');
            
            if (input.value) {
                options.forEach(option => {
                    if (option.getAttribute('data-value') === input.value) {
                        option.classList.add('selected');
                        trigger.textContent = option.textContent;
                        select.classList.add('valid');
                        // 切换图标
                        infoIcon.style.display = 'none';
                        successIcon.style.display = 'inline-block';
                    }
                });
            }
        });

        // 特别处理非必填字段的提示文本
        $("#contact_address, #contact_weixin").each(function() {
            var $this = $(this);
            var $tip = $this.siblings(".inputTip");
            var tipText = $tip.data('tip-text');
            
            // 初始化时保存提示文本
            $tip.data('originalText', tipText);
            
            // 绑定焦点事件
            $this.on('focus', function() {
                if (!$tip.hasClass('success') && !$tip.hasClass('error')) {
                    $tip.addClass('focus')
                        .html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + tipText + '</span>');
                }
            });
            
            // 绑定失去焦点事件
            $this.on('blur', function() {
                if ($this.val()) {
                    $tip.removeClass('focus').addClass('success')
                        .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + tipText + '</span>');
                } else {
                    $tip.removeClass('focus success')
                        .html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + tipText + '</span>');
                }
            });
            
            // 初始化显示
            if ($this.val()) {
                $tip.addClass('success')
                    .html('<i class="fas fa-check-circle"></i> <span class="tip-text">' + tipText + '</span>');
            } else {
                $tip.html('<i class="fas fa-info-circle"></i> <span class="tip-text">' + tipText + '</span>');
            }
        });

        // 添加表单提交事件处理
        $("#submit-btn").on("click", function(e) {
            e.preventDefault(); // 阻止默认行为
            
            // 获取表单元素
            var form = $("#edit-form");
            
            // 手动触发表单验证
            if (!form.valid()) {
                return false; // 如果验证失败，停止执行
            }
            
            // 显示加载遮罩
            $("#loading-overlay").css('display', 'flex').fadeIn();
            
            // 使用FormData获取表单数据
            var formData = new FormData(form[0]);
            
            // 添加已有图片ID
            var existingImages = [];
            $('.image-item input[name="existing_images[]"]').each(function() {
                existingImages.push($(this).val());
            });
            formData.append('existing_images', JSON.stringify(existingImages));
            
            // 添加submit字段，确保服务器端能识别表单提交
            formData.append('submit', '1');
            
            // 添加ajax标记
            formData.append('ajax', '1');
            
            // AJAX提交表单
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                complete: function(xhr, status) {
                    // 删除调试信息，无需记录请求状态
                },
                success: function(response) {
                    // 隐藏加载遮罩
                    $('#loading-overlay').fadeOut();
                    
                    // 尝试解析响应（如果它还不是对象）
                    var responseData = response;
                    if (typeof response === 'string') {
                        try {
                            responseData = JSON.parse(response);
                        } catch (e) {
                            
                            // 如果响应中包含成功信息，视为成功
                            if (response.indexOf('成功') > -1) {
                                $('#success-overlay').css('display', 'flex').fadeIn();
                                return;
                            } else {
                                alert("保存失败: 服务器返回非JSON格式数据");
                                return;
                            }
                        }
                    }
                    
                    // 处理JSON响应
                    if (responseData && responseData.success) {
                        // 显示成功提示
                        // 重置成功弹窗的文本，确保显示正确的成功消息
                        $('.success-text').text('操作成功！');
                        $('.success-subtext').text('您的信息已成功更新');
                        $('#success-btn').text('查看信息');
                        
                        $('#success-overlay').css('display', 'flex').fadeIn();
                        
                        // 表单提交成功，为成功按钮绑定跳转到详情页的事件
                        $('#success-btn').off('click').on('click', function() {
                            if (responseData.detail_url) {
                                window.location.href = responseData.detail_url;
                            } else {
                                window.location.reload();
                            }
                        });
                    } else {
                        // 显示错误信息
                        var message = responseData && responseData.message ? responseData.message : '保存失败，请稍后再试';
                        alert(message);
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏加载遮罩
                    $('#loading-overlay').fadeOut();
                    
                    // 尝试解析错误响应
                    var errorMessage = '保存失败，请稍后再试 (错误: ' + error + ')';
                    try {
                        if (xhr.responseText) {
                            var response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        }
                    } catch (e) {
                        errorMessage += ' (无法解析响应)';
                    }
                    
                    alert(errorMessage);
                }
            });
            
            return false;
        });
        
        // 监听表单的原生提交事件，确保它被拦截
        $("#edit-form").on("submit", function(e) {
            e.preventDefault();
            $("#submit-btn").click(); // 触发提交按钮的点击事件
            return false;
        });
    });
    </script>
</body>
</html>