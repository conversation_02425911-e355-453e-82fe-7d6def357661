<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'pages'): ?>active<?php endif; ?>">
    <a href="pages.php">
        <i class="fas fa-file-alt"></i>
        <span>单页管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'links'): ?>active<?php endif; ?>">
    <a href="links.php">
        <i class="fas fa-link"></i>
        <span>友情链接</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'mobile_security'): ?>active<?php endif; ?>">
    <a href="mobile_security.php">
        <i class="fas fa-shield-alt"></i>
        <span>手机号安全</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });

    // 处理URL参数中的错误和成功消息
    function handleUrlMessages() {
        const urlParams = new URLSearchParams(window.location.search);
        const message = urlParams.get('message');
        const error = urlParams.get('error');

        if (message) {
            showSuccessMessage(message);
            // 清除URL中的message参数
            clearUrlParameter('message');
        }

        if (error) {
            showErrorMessage(error);
            // 清除URL中的error参数
            clearUrlParameter('error');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-check-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    // 显示错误消息
    function showErrorMessage(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                <i class="fas fa-exclamation-circle me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 8秒后自动消失（错误消息显示时间稍长）
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 8000);
    }

    // 清除URL参数
    function clearUrlParameter(param) {
        const url = new URL(window.location);
        url.searchParams.delete(param);
        window.history.replaceState({}, document.title, url.toString());
    }

    // 页面加载完成后处理URL消息
    document.addEventListener('DOMContentLoaded', function() {
        handleUrlMessages();
    });
</script>


<style>
    /* 基础样式 */
    .page-title {
        font-size: 20px;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .page-title h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
    }
    .content-table {
        width: 100%;
        border-collapse: collapse;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    .content-table th {
        background-color: #f8f9fa;
        padding: 10px 15px;
        text-align: left;
        font-weight: 500;
        color: #333;
        border-bottom: 1px solid #dee2e6;
    }
    .content-table td {
        padding: 10px 15px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }
    .content-table tr:last-child td {
        border-bottom: none;
    }
    .content-table tr:hover {
        background-color: #f8f9fa;
    }

    /* 淡色按钮样式 */
    .btn-light-primary {
        background-color: #e6f0ff;
        color: #1b68ff;
        border: 1px solid #cce0ff;
    }
    .btn-light-primary:hover {
        background-color: #d1e3ff;
        color: #0056b3;
    }
    .btn-light-warning {
        background-color: #fff8e6;
        color: #ffa500;
        border: 1px solid #ffe6b3;
    }
    .btn-light-warning:hover {
        background-color: #fff0d1;
        color: #cc8400;
    }
    .btn-light-danger {
        background-color: #ffe6e6;
        color: #ff3333;
        border: 1px solid #ffb3b3;
    }
    .btn-light-danger:hover {
        background-color: #ffd1d1;
        color: #cc0000;
    }
    .btn-light-info {
        background-color: #e6f7ff;
        color: #00aaff;
        border: 1px solid #b3e0ff;
    }
    .btn-light-info:hover {
        background-color: #d1f0ff;
        color: #0088cc;
    }
    .btn-light-success {
        background-color: #e6ffe6;
        color: #00aa00;
        border: 1px solid #b3ffb3;
    }
    .btn-light-success:hover {
        background-color: #d1ffd1;
        color: #008800;
    }
    .btn-light-secondary {
        background-color: #f0f0f0;
        color: #666666;
        border: 1px solid #dddddd;
    }
    .btn-light-secondary:hover {
        background-color: #e0e0e0;
        color: #444444;
    }
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
        line-height: 1.5;
        border-radius: 0.25rem;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        cursor: pointer;
        text-decoration: none;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .alert {
        position: relative;
        padding: 12px 20px;
        margin-bottom: 16px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
    .alert-success {
        color: #155724;
        background-color: #d4edda;
        border-color: #c3e6cb;
    }
    .alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    .badge {
        display: inline-block;
        padding: 0.25em 0.5em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    .bg-success {
        background-color: #28a745 !important;
        color: #fff;
    }
    .bg-danger {
        background-color: #dc3545 !important;
        color: #fff;
    }
    .text-center {
        text-align: center;
    }
    .text-right {
        text-align: right;
    }
    
    /* 分页样式 */
    .pagination-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 20px 0;
    }
    .simple-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 5px;
    }
    .pagination-btn {
        display: inline-block;
        padding: 5px 12px;
        background: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-decoration: none;
        border-radius: 3px;
        transition: all 0.2s;
    }
    .pagination-btn:hover {
        background: #f8f9fa;
        border-color: #ccc;
    }
    .pagination-btn.active {
        background: #1b68ff;
        color: white;
        border-color: #1b68ff;
    }
    .pagination-btn.disabled {
        color: #aaa;
        background: #f8f8f8;
        cursor: not-allowed;
    }
    .category-actions {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-end;
        gap: 5px;
        min-width: 120px;
    }
    .category-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        white-space: nowrap;
        text-decoration: none !important;
    }
    a, a:hover, a:focus, a:active {
        text-decoration: none !important;
    }
    .category-level-1 { font-weight: 500; }
    .category-level-2 { padding-left: 15px; }
    .filter-section {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 20px;
        padding: 15px;
    }
    .filter-form {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 10px;
    }
    .filter-form label {
        margin-bottom: 0;
        margin-right: 5px;
        font-weight: 500;
    }
    .filter-form .form-control {
        width: auto; 
        display: inline-block;
        padding: 4px 8px;
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.15s ease-in-out;
    }
    .filter-form button, .filter-form .btn {
        margin-left: 5px;
    }
    .filter-form-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }
    .category-status-badge {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .status-active {
        background-color: #28a745;
    }
    .status-inactive {
        background-color: #dc3545;
    }
    /* 筛选标签样式 */
    .filter-tag {
        display: inline-block;
        padding: 6px 10px;
        font-size: 12px;
        font-weight: normal;
        color: #fff;
        text-decoration: none;
        border-radius: 4px;
        margin-right: 5px;
        margin-bottom: 5px;
    }
    .filter-tag.active-all { background-color: #007bff; }
    .filter-tag.inactive-all { background-color: #6c757d; }
    .filter-tag.active-first { background-color: #007bff; }
    .filter-tag.inactive-first { background-color: #6c757d; }
    .filter-tag.active-second { background-color: #007bff; }
    .filter-tag.inactive-second { background-color: #6c757d; }

    /* 状态标签样式 */
    .tag {
        display: inline-block;
        padding: 2px 6px;
        font-size: 11px;
        font-weight: normal;
        color: #fff;
        border-radius: 3px;
        text-align: center;
        white-space: nowrap;
    }
    .tag-success {
        background-color: #28a745;
        color: #fff;
    }
    .tag-danger {
        background-color: #dc3545;
        color: #fff;
    }
    .tag-warning {
        background-color: #ffc107;
        color: #212529;
    }
    .tag-primary {
        background-color: #007bff;
        color: #fff;
    }
</style>

<div class="container-fluid">
    <!-- 消息提示 -->
    <?php if(null !== ($message ?? null) && $message != ''): ?>
    <div class="alert alert-success"><?php echo $message ?? ""; ?></div>
    <?php endif; ?>
    <?php if(null !== ($error ?? null) && $error != ''): ?>
    <div class="alert alert-danger"><?php echo $error ?? ""; ?></div>
    <?php endif; ?>

    <!-- 新闻栏目管理 -->
    <div class="card mb-4">
        <div class="card-body">
            <!-- 高级筛选表单 -->
            <div style="margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #e9ecef;">
                <form action="news_category.php" method="get" style="display: flex; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">栏目级别:</span>
                        <select name="parent_id" id="parent_id" style="width: 150px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                            <option value="-1" <?php if($parent_id == -1): ?>selected<?php endif; ?>>一级栏目</option>
                            <option value="-2" <?php if($parent_id == -2): ?>selected<?php endif; ?>>二级栏目</option>
                            <option value="0" <?php if($parent_id == 0): ?>selected<?php endif; ?>>所有栏目</option>
                            <?php if(null !== ($parentCategories ?? null) && count($parentCategories) > 0): ?>
                            <optgroup label="特定父栏目的子栏目">
                                <?php if(null !== ($parentCategories ?? null) && is_array($parentCategories)): foreach($parentCategories as $cat): ?>
                                <option value="<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" <?php if($parent_id == $cat['catid']): ?>selected<?php endif; ?>><?php echo (isset($cat['catname'])) ? $cat['catname'] : ""; ?>的子栏目</option>
                                <?php endforeach; endif; ?>
                            </optgroup>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">栏目关键字:</span>
                        <input type="text" name="keyword" value="<?php echo $keyword ?? ""; ?>" placeholder="搜索栏目名称..." style="width: 200px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                    </div>

                    <div style="display: flex; align-items: center; white-space: nowrap;">
                        <span style="margin-right: 5px; color: #333;">状态:</span>
                        <select name="status" style="width: 120px; height: 32px; border: 1px solid #e9ecef; border-radius: 4px; padding: 0 8px;">
                            <option value="-1" <?php if($status == -1): ?>selected<?php endif; ?>>全部</option>
                            <option value="1" <?php if($status == 1): ?>selected<?php endif; ?>>启用</option>
                            <option value="0" <?php if($status == 0): ?>selected<?php endif; ?>>禁用</option>
                        </select>
                    </div>
                    
                    <div>
                        <button type="submit" class="btn btn-sm btn-light-primary" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">筛选</button>
                        <a href="news_category.php" class="btn btn-sm btn-light-secondary" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">重置</a>
                    </div>
                    
                    <div style="margin-left: auto;">
                        <a href="news_category.php?op=add" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">添加栏目</a>
                        <?php if($parent_id > 0): ?>
                        <a href="news_category.php?op=add&parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-sm btn-light-success" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">添加子栏目</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <!-- 栏目列表 -->
            <!-- 批量操作表单 -->
            <form id="batchForm" action="news_category.php?op=batch_delete" method="post">
                <input type="hidden" name="return_parent_id" value="<?php echo $parent_id ?? ""; ?>">
                
                <div class="table-responsive">
                    <table class="content-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>栏目名称</th>
                                <th>排序</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(null !== ($categories ?? null) && count($categories) > 0): ?>
                            <?php if(null !== ($categories ?? null) && is_array($categories)): foreach($categories as $cat): ?>
                            <tr>
                                <td><?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?></td>
                                <td class="category-level-<?php echo (isset($cat['level'])) ? $cat['level'] : ""; ?>"><?php echo (isset($cat['catname'])) ? $cat['catname'] : ""; ?></td>
                                <td><?php echo (isset($cat['sort_order'])) ? $cat['sort_order'] : ""; ?></td>
                                <td>
                                    <?php if($cat['is_show'] == 1): ?>
                                    <span class="tag tag-success">启用</span>
                                    <?php else: ?>
                                    <span class="tag tag-danger">禁用</span>
                                    <?php endif; ?>
                                </td>
                                <td class="category-actions">
                                    <a href="../news.php?catid=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" class="btn btn-sm btn-light-info" target="_blank">前台</a>
                                    <?php if($cat['has_children'] > 0): ?>
                                    <a href="news_category.php?parent_id=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" class="btn btn-sm btn-light-primary">子栏目</a>
                                    <?php endif; ?>
                                    <?php if($cat['level'] == 1): ?>
                                    <a href="news_category.php?op=add&parent_id=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" class="btn btn-sm btn-light-success">添加子栏目</a>
                                    <?php endif; ?>
                                    <a href="news_category.php?op=edit&catid=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>" class="btn btn-sm btn-light-primary">编辑</a>
                                    <?php if($cat['is_show'] == 1): ?>
                                    <a href="news_category.php?op=toggle_status&catid=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>&return_parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-sm btn-light-warning" onclick="return confirm('确定要禁用此栏目吗？');">禁用</a>
                                    <?php else: ?>
                                    <a href="news_category.php?op=toggle_status&catid=<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>&return_parent_id=<?php echo $parent_id ?? ""; ?>" class="btn btn-sm btn-light-success" onclick="return confirm('确定要启用此栏目吗？');">启用</a>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-sm btn-light-danger" onclick="deleteCategory(<?php echo (isset($cat['catid'])) ? $cat['catid'] : ""; ?>)">删除</button>
                                </td>
                            </tr>
                            <?php endforeach; endif; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center">暂无栏目数据</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作按钮和分页 -->
                <div style="margin-top: 15px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                    <!-- 批量操作 -->
                    <div style="margin-bottom: 15px;">
                        <button type="button" id="batch_delete_btn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 32px; padding: 0 12px; display: inline-flex; align-items: center; justify-content: center;">批量删除</button>
                        <div id="selection_count" style="color: #666; display: inline-block; margin-left: 10px;">已选择 0 个栏目</div>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if(null !== ($pagination ?? null) && $total > 0): ?>
                    <div style="flex: 1; text-align: right;">
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            <?php echo $pagination_html ?? ""; ?>
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 <?php echo (isset($pagination['total_page'])) ? $pagination['total_page'] : ""; ?> 页</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->

    <!-- jQuery (必须在Bootstrap之前加载) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 设置当前菜单选中状态
    var menuElement = document.getElementById("menu_news_category");
    if (menuElement) {
        menuElement.className += " active";
    }
    
    // 全选功能
    var selectAllCheckbox = document.getElementById('select_all');
    var categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    var selectionCount = document.getElementById('selection_count');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            var isChecked = this.checked;
            categoryCheckboxes.forEach(function(checkbox) {
                checkbox.checked = isChecked;
            });
            updateSelectionCount();
        });
    }
    
    // 更新选中数量
    categoryCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            updateSelectionCount();
            
            // 检查是否所有复选框都被选中
            var allChecked = true;
            categoryCheckboxes.forEach(function(cb) {
                if (!cb.checked) {
                    allChecked = false;
                }
            });
            
            // 更新全选复选框状态
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
            }
        });
    });
    
    // 更新选中计数
    function updateSelectionCount() {
        var count = 0;
        categoryCheckboxes.forEach(function(checkbox) {
            if (checkbox.checked) {
                count++;
            }
        });
        
        if (selectionCount) {
            selectionCount.textContent = '已选择 ' + count + ' 个栏目';
        }
    }
    
    // 批量删除按钮
    var batchDeleteBtn = document.getElementById('batch_delete_btn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
            var checkedCount = 0;
            var hasChecked = false;
            
            categoryCheckboxes.forEach(function(checkbox) {
                if (checkbox.checked) {
                    hasChecked = true;
                    checkedCount++;
                }
            });
            
            if (!hasChecked) {
                alert('请先选择要删除的栏目');
                return false;
            }
            
            if (confirm('确定要删除选中的 ' + checkedCount + ' 个栏目吗？此操作不可恢复！')) {
                // 提交表单前确保有被选中的checkbox
                if (checkedCount > 0) {
                    document.getElementById('batchForm').submit();
                } else {
                    alert('请先选择要删除的栏目');
                    return false;
                }
            }
        });
    }
    
    // 过滤器表单下拉框自动提交
    var parentIdSelect = document.getElementById('parent_id');
    if (parentIdSelect) {
        parentIdSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});

// 删除栏目
function deleteCategory(catid) {
    if (confirm('确定要删除该栏目吗？此操作不可恢复！')) {
        location.href = 'news_category.php?op=delete&catid=' + catid;
    }
}
</script> 