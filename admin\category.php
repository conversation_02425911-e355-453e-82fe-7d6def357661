<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 分类管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 加载拼音转换类
require_once(INCLUDE_PATH . 'pinyin.class.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'category';

// 操作类型
$action = isset($_GET['action']) ? $_GET['action'] : 'list';

// 信息提示
$message = '';
$error = '';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 处理分类操作
switch ($action) {
    // 分类列表
    case 'list':
        // 获取当前页码
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $per_page = 10;
        
        // 获取父分类ID（用于筛选）
        $parent_id = isset($_GET['parent_id']) ? intval($_GET['parent_id']) : -1; // 默认只显示一级分类(-1)
        
        // 获取视图模式
        $view_mode = isset($_GET['view_mode']) ? trim($_GET['view_mode']) : 'parent_only';
        
        // 获取关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';

        // 获取状态筛选参数
        $status = isset($_GET['status']) ? intval($_GET['status']) : -1; // -1:全部, 0:禁用, 1:启用
        
        // 先获取所有符合条件的分类
        $all_matching_categories = [];
        
        // 当有关键词时，始终搜索所有分类
        if (!empty($keyword)) {
            $all_matching_categories = get_filtered_categories(null, $keyword, false, false, $status);
            
            // 为每个分类添加level属性和parent_name属性
            foreach ($all_matching_categories as &$cat) {
                if ($cat['parent_id'] == 0) {
                    $cat['level'] = 1;
                } else {
                    $cat['level'] = 2;
                    $cat['parent_name'] = get_parent_name($cat['parent_id']);
                }
            }
        } else {
            // 没有关键词时，按照parent_id筛选
            if ($parent_id > 0) {
                // 筛选特定父分类下的子分类
                $all_matching_categories = get_filtered_categories($parent_id, '', false, false, $status);
                // 对于特定父分类的筛选，我们不需要构建树形结构，直接设置level值
                foreach ($all_matching_categories as &$cat) {
                    $cat['level'] = 2; // 设置为二级分类
                    $cat['parent_name'] = get_parent_name($cat['parent_id']); // 获取父分类名称
                }
            } else if ($parent_id == -1) {
                // 只显示一级分类
                $all_matching_categories = get_filtered_categories(null, '', true, false, $status);
                // 设置所有分类为一级
                foreach ($all_matching_categories as &$cat) {
                    $cat['level'] = 1;
                }
            } else if ($parent_id == -2) {
                // 只显示二级分类
                $all_matching_categories = get_filtered_categories(null, '', false, true, $status);
                // 设置所有分类为二级
                foreach ($all_matching_categories as &$cat) {
                    $cat['level'] = 2;
                    $cat['parent_name'] = get_parent_name($cat['parent_id']); // 获取父分类名称
                }
            } else {
                // 显示所有分类
                $all_matching_categories = get_filtered_categories(null, '', false, false, $status);
                // 为每个分类添加level属性和parent_name属性
                foreach ($all_matching_categories as &$cat) {
                    if ($cat['parent_id'] == 0) {
                        $cat['level'] = 1;
                    } else {
                        $cat['level'] = 2;
                        $cat['parent_name'] = get_parent_name($cat['parent_id']);
                    }
                }
            }
        }
        
        // 计算总数
        $total_categories = count($all_matching_categories);
        
        // 分页处理
        $offset = ($page - 1) * $per_page;
        $categories = array_slice($all_matching_categories, $offset, $per_page);
        
        // 获取所有一级分类，用于筛选下拉框
        $all_categories = get_all_categories();
        $parentCategories = array_filter($all_categories, function($cat) {
            return $cat['parent_id'] == 0;
        });
        
        // 生成分页数据
        $pagination = generate_pagination($total_categories, $page, $per_page, 5);
        
        // 构建分页链接
        $base_url = '?';
        $params = '';
        if ($parent_id !== 0) {
            $params .= '&parent_id=' . $parent_id;
        }
        if (!empty($keyword)) {
            $params .= '&keyword=' . urlencode($keyword);
        }
        if (!empty($view_mode)) {
            $params .= '&view_mode=' . $view_mode;
        }
        
        // 设置分页模板变量
        set_pagination_template_vars($pagination, $base_url, $params);
        
        // 设置模板变量
        $tpl->assign('categories', $categories);
        $tpl->assign('parentCategories', $parentCategories);
        $tpl->assign('parent_id', $parent_id);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('view_mode', $view_mode);
        $tpl->assign('status', $status);
        $tpl->assign('breadcrumb', '分类管理');
        
        // 如果是根据特定父分类筛选，获取父分类名称
        if ($parent_id > 0) {
            $parent_category = get_category($parent_id);
            if ($parent_category) {
                $tpl->assign('current_parent_name', $parent_category['name']);
            }
        }
        
        // 不在这里显示模板，统一在文件末尾显示
        break;
        
    // 批量删除分类
    case 'batch_delete':
        // 检查是否有选中的分类ID
        if (!isset($_POST['category_ids']) || !is_array($_POST['category_ids']) || empty($_POST['category_ids'])) {
            $error = '未选择要删除的分类';
            break;
        }
        
        // 获取当前parent_id，用于操作后返回相同视图
        $return_parent_id = isset($_POST['return_parent_id']) ? intval($_POST['return_parent_id']) : -1;
        
        $category_ids = array_map('intval', $_POST['category_ids']);
        $success_count = 0;
        $error_count = 0;
        $has_children_count = 0;
        $has_posts_count = 0;
        
        foreach ($category_ids as $id) {
            // 检查分类是否有子分类
            $children = get_direct_children($id);
            if (count($children) > 0) {
                $has_children_count++;
                continue;
            }
            
            // 执行删除，不再检查分类是否有关联的信息
            if (delete_category($id)) {
                $success_count++;
            } else {
                $error_count++;
            }
        }
        
        // 提示信息
        if ($success_count > 0) {
            // 清理分类相关缓存
            if (function_exists('clearCategoryCache')) {
                clearCategoryCache();
            }

            $message = "成功删除 {$success_count} 个分类";
            if ($has_children_count > 0) {
                $message .= "，{$has_children_count} 个分类有子分类未删除";
            }
            if ($error_count > 0) {
                $message .= "，{$error_count} 个分类删除失败";
            }
        } else {
            if ($has_children_count > 0) {
                $message = "{$has_children_count} 个分类有子分类，请先删除子分类";
            } else {
                $message = "删除失败";
            }
        }
        
        // 重定向到之前的视图
        $redirect_url = "category.php?message=" . urlencode($message);
        if ($return_parent_id != -1) {
            $redirect_url .= "&parent_id=" . $return_parent_id;
        }
        header("Location: " . $redirect_url);
        exit;
        break;
        
    // 添加分类
    case 'add':
        // 获取所有分类作为父分类选项
        $parent_categories = get_all_categories();
        $parent_categories = build_category_tree($parent_categories);
        
        // 修改为只获取一级分类
        $parent_categories = array_filter($parent_categories, function($item) {
            return $item['parent_id'] == 0 && $item['level'] == 0;
        });
        
        // 获取URL中的parent_id参数
        $selected_parent_id = isset($_GET['parent_id']) ? intval($_GET['parent_id']) : 0;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_category();
            if ($result['success']) {
                $message = '分类添加成功';
                
                // 获取选择的父分类ID，用于重定向
                $parent_id_for_redirect = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
                
                // 重定向到列表页，如果设置了父分类则返回到对应的父分类列表
                header("Location: category.php?message=" . urlencode($message) . ($parent_id_for_redirect > 0 ? "&parent_id=" . $parent_id_for_redirect : ""));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        // 获取所有可用的模板文件
        $template_files = get_template_files('category_');
        $detail_template_files = get_template_files('view_');
        
        $tpl->assign('template_files', $template_files);
        $tpl->assign('detail_template_files', $detail_template_files);
        $tpl->assign('parent_categories', $parent_categories);
        $tpl->assign('is_top_level', false);
        $tpl->assign('selected_parent_id', $selected_parent_id);
        break;
        
    // 编辑分类
    case 'edit':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定分类ID';
            break;
        }
        
        // 获取分类信息
        $category = get_category($id);
        if (!$category) {
            $error = '分类不存在';
            break;
        }
        
        // 获取所有分类作为父分类选项，排除自身及其子分类
        $parent_categories = get_all_categories();
        $excluded_ids = get_category_children($id);
        $excluded_ids[] = $id;
        $filtered_categories = array_filter($parent_categories, function($item) use ($excluded_ids) {
            return !in_array($item['id'], $excluded_ids);
        });
        $parent_categories = build_category_tree($filtered_categories);
        
        // 修改为只获取一级分类
        $parent_categories = array_filter($parent_categories, function($item) {
            return $item['parent_id'] == 0 && $item['level'] == 0;
        });
        
        // 标记当前编辑的分类是否为一级分类
        $is_top_level = ($category['parent_id'] == 0);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_category($id);
            if ($result['success']) {
                $message = '分类更新成功';
                
                // 获取当前分类的parent_id，用于重定向回正确的分类列表
                $category_data = get_category($id);
                $parent_id_for_redirect = ($category_data && $category_data['parent_id'] > 0) ? $category_data['parent_id'] : -1;
                
                // 重定向到列表页，如果是二级分类则返回到对应的父分类列表，如果是一级分类则返回到一级分类列表视图
                header("Location: category.php?message=" . urlencode($message) . "&parent_id=" . $parent_id_for_redirect);
                exit;
            } else {
                $error = $result['error'];
            }
        }
        
        // 获取所有可用的模板文件
        $template_files = get_template_files('category_');
        $detail_template_files = get_template_files('view_');
        
        $tpl->assign('template_files', $template_files);
        $tpl->assign('detail_template_files', $detail_template_files);
        $tpl->assign('parent_categories', $parent_categories);
        $tpl->assign('is_top_level', $is_top_level);
        $tpl->assign('selected_parent_id', $selected_parent_id);
        break;
        
    // 删除分类
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定分类ID';
            break;
        }
        
        // 获取当前parent_id，用于操作后返回相同视图
        $return_parent_id = isset($_GET['return_parent_id']) ? intval($_GET['return_parent_id']) : -1;
        
        // 检查分类是否有子分类
        $children = get_direct_children($id);
        if (count($children) > 0) {
            $error = '该分类下有子分类，请先移除或删除子分类';
            break;
        }
        
        // 删除分类，不再检查分类是否有关联的信息
        if (delete_category($id)) {
            $message = '分类删除成功';
            // 重定向到之前的视图
            $redirect_url = "category.php?message=" . urlencode($message);
            if ($return_parent_id != -1) {
                $redirect_url .= "&parent_id=" . $return_parent_id;
            }
            header("Location: " . $redirect_url);
            exit;
        } else {
            $error = '删除分类失败';
        }
        break;
        
    // 更改状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定分类ID';
            break;
        }
        
        // 更改状态
        if (toggle_category_status($id)) {
            $message = '分类状态已更改';
            // 重定向到列表页
            header("Location: category.php?message=" . urlencode($message));
            exit;
        } else {
            $error = '更改分类状态失败';
        }
        break;
        
    // 批量添加
    case 'batch':
        // 获取所有分类作为父分类选项
        $parent_categories = get_all_categories();
        $parent_categories = build_category_tree($parent_categories);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理批量添加
            $result = batch_add_categories();
            if ($result['success']) {
                $message = '成功添加 ' . $result['count'] . ' 个分类';
                // 重定向到列表页
                header("Location: category.php?message=" . urlencode($message));
                exit;
            } else {
                $error = $result['error'];
            }
        }
        break;
        
    // 检查拼音是否存在
    case 'check_pinyin':
        header('Content-Type: application/json');
        $pinyin = isset($_GET['pinyin']) ? trim($_GET['pinyin']) : '';
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if (empty($pinyin)) {
            echo json_encode(['valid' => false, 'message' => '拼音不能为空']);
            exit;
        }
        
        $exists = pinyin_exists($pinyin, $id);
        echo json_encode(['valid' => !$exists, 'message' => $exists ? '该拼音已被使用' : '']);
        exit;
        
    // 生成拼音
    case 'generate_pinyin':
        header('Content-Type: application/json');
        $name = isset($_GET['name']) ? trim($_GET['name']) : '';
        
        if (empty($name)) {
            echo json_encode(['pinyin' => '']);
            exit;
        }
        
        // 记录原始输入
        error_log("生成拼音输入: " . $name);
        
        $pinyin = generate_pinyin($name);
        
        // 记录生成结果
        error_log("生成拼音结果: " . $pinyin);
        
        echo json_encode(['pinyin' => $pinyin]);
        exit;
        
    // 保存排序
    case 'save_sort':
        // 检查是否是POST请求
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            exit(json_encode(['success' => false, 'message' => '无效的请求方法']));
        }

        try {
            // 获取原始POST数据
            $raw_data = file_get_contents('php://input');
            $post_data = [];
            parse_str($raw_data, $post_data);
            
            // 解析JSON数据
            $sort_array = json_decode($post_data['sort_data'], true);
            
            if (!is_array($sort_array)) {
                exit(json_encode(['success' => false, 'message' => '数据格式错误：' . json_last_error_msg()]));
            }

            // 开始事务
            $db->beginTransaction();

            // 更新每个分类的排序
            foreach ($sort_array as $item) {
                if (!isset($item['id']) || !isset($item['sort'])) {
                    continue;
                }
                
                $id = intval($item['id']);
                $sort = intval($item['sort']);
                
                if ($id > 0) {
                    // 更新排序
                    $sql = "UPDATE categories SET sort_order = $sort WHERE id = $id";
                    $db->query($sql);
                }
            }

            // 提交事务
            $db->commit();
            
       
            
            exit(json_encode(['success' => true, 'message' => '排序保存成功']));
        } catch (Exception $e) {
            // 回滚事务
            if ($db->inTransaction()) {
                $db->rollBack();
            }
            exit(json_encode(['success' => false, 'message' => $e->getMessage()]));
        }
        break;
        
    // 保存更改（排序和拼音）
    case 'save_changes':
        // 处理表单提交
        $sort_data = isset($_POST['sort']) ? $_POST['sort'] : [];
        $pinyin_data = isset($_POST['pinyin']) ? $_POST['pinyin'] : [];
        
        // 检查是否有数据需要更新
        if (empty($sort_data) && empty($pinyin_data)) {
            header("Location: category.php?message=" . urlencode('没有修改数据，无需保存'));
            exit;
        }
        
        try {
            // 开始事务
            $db->beginTransaction();
            $update_count = 0;
            
            // 更新排序和拼音
            foreach ($sort_data as $id => $sort) {
                $id = intval($id);
                $sort = intval($sort);
                $pinyin = isset($pinyin_data[$id]) ? trim($pinyin_data[$id]) : '';
                
                if ($id > 0) {
                    $data = [];
                    
                    // 只更新有值的字段
                    if ($sort >= 0) {
                        $data['sort_order'] = $sort;
                    }
                    
                    if (!empty($pinyin)) {
                        // 检查拼音唯一性
                        if (pinyin_exists($pinyin, $id)) {
                            throw new Exception("标识符 '{$pinyin}' 已存在，请修改后重试");
                        }
                        $data['pinyin'] = $pinyin;
                    }
                    
                    if (!empty($data)) {
                        $db->update('categories', $data, "id = $id");
                        $update_count++;
                    }
                }
            }
            
            // 提交事务
            $db->commit();

            // 清理分类相关缓存
            if (function_exists('clearCategoryCache')) {
                clearCategoryCache();
            }

            header("Location: category.php?message=" . urlencode("成功更新 {$update_count} 个分类"));
            exit;
        } catch (Exception $e) {
            // 回滚事务
            if ($db->inTransaction()) {
                $db->rollback();
            }
            
            header("Location: category.php?error=" . urlencode($e->getMessage()));
            exit;
        }
        break;
        
    default:
        $error = '未知操作';
        break;
}

// 接收URL传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

// 接收URL传递的错误信息
if (isset($_GET['error']) && empty($error)) {
    $error = $_GET['error'];
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '分类管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '分类管理');
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作分配特定数据
switch ($action) {
    case 'list':
        $tpl->assign('categories', $categories);
        $tpl->assign('pagination', $pagination);
        $tpl->display('category_list.htm');
        break;
        
    case 'add':
        $tpl->assign('parent_categories', $parent_categories);
        $tpl->assign('is_top_level', false);
        $tpl->assign('selected_parent_id', $selected_parent_id);
        $tpl->display('category_edit.htm');
        break;
        
    case 'edit':
        $tpl->assign('category', $category);
        $tpl->assign('parent_categories', $parent_categories);
        $tpl->assign('is_top_level', $is_top_level);
        $tpl->display('category_edit.htm');
        break;
        
    case 'batch':
        $tpl->assign('parent_categories', $parent_categories);
        $tpl->display('category_batch.htm');
        break;
        
    default:
        $tpl->display('category_list.htm');
        break;
}

/**
 * 获取所有分类
 */
function get_all_categories() {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 按ID排序
    usort($categories, function($a, $b) {
        return $a['id'] - $b['id'];
    });
    
    return $categories;
}

/**
 * 获取分类总数
 */
function get_categories_count($parent_id = null, $only_top_level = false, $keyword = '', $only_second_level = false) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 根据条件筛选分类
    $filtered_categories = array_filter($categories, function($category) use ($parent_id, $only_top_level, $keyword, $only_second_level) {
        // 不再过滤禁用的分类，显示所有状态的分类
        
        // 父级ID筛选
        if ($parent_id !== null && $category['parent_id'] != $parent_id) {
            return false;
        }
        
        // 仅顶级分类筛选
        if ($only_top_level && $category['parent_id'] != 0) {
            return false;
        }
        
        // 仅二级分类筛选
        if ($only_second_level && $category['parent_id'] == 0) {
            return false;
        }
        
        // 关键词筛选
        if (!empty($keyword) && strpos($category['name'], $keyword) === false) {
            return false;
        }
        
        return true;
    });
    
    return count($filtered_categories);
}

/**
 * 获取分页分类数据
 */
function get_categories($page = 1, $per_page = 10, $parent_id = null, $keyword = '', $only_top_level = false, $only_second_level = false) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 根据条件筛选分类
    $filtered_categories = array_filter($categories, function($category) use ($parent_id, $only_top_level, $keyword, $only_second_level) {
        // 不再过滤禁用的分类，显示所有状态的分类
        
        // 父级ID筛选
        if ($parent_id !== null && $category['parent_id'] != $parent_id) {
            return false;
        }
        
        // 仅顶级分类筛选
        if ($only_top_level && $category['parent_id'] != 0) {
            return false;
        }
        
        // 仅二级分类筛选
        if ($only_second_level && $category['parent_id'] == 0) {
            return false;
        }
        
        // 关键词筛选
        if (!empty($keyword) && strpos($category['name'], $keyword) === false) {
            return false;
        }
        
        return true;
    });
    
    // 重新索引数组，确保索引连续，这对分页很重要
    $filtered_categories = array_values($filtered_categories);
    
    // 对结果进行排序
    usort($filtered_categories, function($a, $b) {
        return $a['id'] - $b['id']; // 按ID升序排序，与原来的SQL保持一致
    });
    
    // 分页处理
    $offset = ($page - 1) * $per_page;
    $paged_categories = array_slice($filtered_categories, $offset, $per_page);
    
    return $paged_categories;
}

/**
 * 将分类列表构建为树形结构
 */
function build_category_tree($categories, $parent_id = 0, $level = 0) {
    $tree = [];
    
    foreach ($categories as $category) {
        if ($category['parent_id'] == $parent_id) {
            $category['level'] = $level;
            $tree[] = $category;
            $children = build_category_tree($categories, $category['id'], $level + 1);
            $tree = array_merge($tree, $children);
        }
    }
    
    return $tree;
}

/**
 * 获取单个分类信息
 */
function get_category($id) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 查找指定ID的分类
    foreach ($categories as $category) {
        if ($category['id'] == $id) {
            return $category;
        }
    }
    
    // 如果缓存中没有找到，再尝试从数据库读取（包括未启用的分类）
    global $db;
    $sql = "SELECT * FROM categories WHERE id = '" . intval($id) . "' LIMIT 1";
    $result = $db->query($sql);
    
    if ($db->num_rows($result) > 0) {
        return $db->fetch_array($result);
    }
    
    return false;
}

/**
 * 检查拼音是否已存在
 */
function pinyin_exists($pinyin, $exclude_id = 0) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 查找是否有相同拼音的分类
    foreach ($categories as $category) {
        // 检查pinyin键是否存在
        if (isset($category['pinyin']) && $category['pinyin'] == $pinyin && $category['id'] != $exclude_id) {
            return true;
        }
    }
    
    // 如果缓存中没有找到，再尝试从数据库读取（可能包含未启用的分类）
    global $db;
    $sql = "SELECT id FROM categories WHERE pinyin = '" . $db->escape($pinyin) . "'";
    if ($exclude_id > 0) {
        $sql .= " AND id != " . intval($exclude_id);
    }
    $sql .= " LIMIT 1";
    
    $result = $db->query($sql);
    return $db->num_rows($result) > 0;
}

/**
 * 生成拼音
 */
function generate_pinyin($text) {
    // 记录原始输入
    error_log("生成拼音输入: " . $text);
    
    // 检查是否包含中文字符
    if (!preg_match("/[\x{4e00}-\x{9fa5}]/u", $text)) {
        error_log("输入不包含中文，直接返回原文");
        // 如果不包含中文，直接转为小写并返回
        $result = strtolower($text);
        $result = preg_replace('/[^a-z0-9]+/', '-', $result);
        $result = trim($result, '-');
        return $result;
    }
    
    $pinyin = new Pinyin();
    $result = $pinyin->convert($text);
    
    // 记录转换后的结果
    error_log("转换结果: " . $result);
    
    // 处理结果确保URL友好
    $result = strtolower($result);
    $result = preg_replace('/[^a-z0-9]+/', '-', $result);
    $result = trim($result, '-');
    
    // 如果生成的拼音为空或都是a，使用一个随机字符串
    if (empty($result) || preg_match('/^a+$/', $result)) {
        error_log("拼音异常，生成随机字符串");
        $result = 'cat-' . substr(md5(uniqid()), 0, 8);
    }
    
    // 确保拼音唯一
    $original = $result;
    $counter = 1;
    
    while (pinyin_exists($result)) {
        $result = $original . '-' . $counter;
        $counter++;
    }
    
    error_log("最终拼音: " . $result);
    return $result;
}

/**
 * 保存分类信息
 */
function save_category($id = 0) {
    global $db, $cache;
    
    // 获取表单数据
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
    $icon = isset($_POST['icon']) ? trim($_POST['icon']) : '';
    $sort_order = isset($_POST['sort_order']) ? intval($_POST['sort_order']) : 0;
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $seo_title = isset($_POST['seo_title']) ? trim($_POST['seo_title']) : '';
    $seo_keywords = isset($_POST['seo_keywords']) ? trim($_POST['seo_keywords']) : '';
    $seo_description = isset($_POST['seo_description']) ? trim($_POST['seo_description']) : '';
    $template = isset($_POST['template']) ? trim($_POST['template']) : '';
    $detail_template = isset($_POST['detail_template']) ? trim($_POST['detail_template']) : '';
    $sync_template = isset($_POST['sync_template']) ? (bool)$_POST['sync_template'] : false;
    $sync_detail_template = isset($_POST['sync_detail_template']) ? (bool)$_POST['sync_detail_template'] : false;
    
    // 验证必填字段
    if (empty($name)) {
        return array('success' => false, 'error' => '请输入分类名称');
    }
    
    // 处理拼音
    $pinyin = isset($_POST['pinyin']) ? trim($_POST['pinyin']) : '';
    if (empty($pinyin)) {
        // 自动生成拼音
        $pinyin = generate_pinyin($name);
    }
    
    // 检查拼音是否已存在
    if (pinyin_exists($pinyin, $id)) {
        return array('success' => false, 'error' => "拼音'{$pinyin}'已存在，请修改");
    }
    
    // 检查是否为批量添加
    $is_batch = isset($_POST['is_batch']) && $_POST['is_batch'] == 1;
    if ($is_batch) {
        return batch_add_categories();
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 执行数据库操作
        if ($id > 0) {
            // 更新现有分类
            $sql = "UPDATE categories SET name = ?, parent_id = ?, icon = ?, sort_order = ?, status = ?, pinyin = ?, 
                    description = ?, seo_title = ?, seo_keywords = ?, seo_description = ?, template = ?, detail_template = ? 
                    WHERE id = ?";
            $result = $db->query($sql, array($name, $parent_id, $icon, $sort_order, $status, $pinyin, 
                            $description, $seo_title, $seo_keywords, $seo_description, $template, $detail_template, $id));
            
            if (!$result) {
                throw new Exception("更新分类失败");
            }
            
            // 获取子栏目ID列表
            $children_ids = get_category_children($id);
            
            // 如果选择了同步子栏目列表页模板，更新所有子栏目的列表页模板设置
            if ($sync_template && !empty($children_ids)) {
                $update_sql = "UPDATE categories SET template = ? WHERE id IN (" . implode(',', $children_ids) . ")";
                $db->query($update_sql, array($template));
            }
            
            // 如果选择了同步子栏目详情页模板，更新所有子栏目的详情页模板设置
            if ($sync_detail_template && !empty($children_ids)) {
                $update_sql = "UPDATE categories SET detail_template = ? WHERE id IN (" . implode(',', $children_ids) . ")";
                $db->query($update_sql, array($detail_template));
            }
        } else {
            // 添加新分类
            $now = time();
            $sql = "INSERT INTO categories (name, parent_id, icon, sort_order, status, pinyin, description, 
                    seo_title, seo_keywords, seo_description, template, detail_template, created_at, updated_at, subcategory_ids) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '')";
            $result = $db->query($sql, array($name, $parent_id, $icon, $sort_order, $status, $pinyin, 
                            $description, $seo_title, $seo_keywords, $seo_description, $template, $detail_template, $now, $now));
            
            if (!$result) {
                throw new Exception("添加分类失败");
            }
            
            // 获取新分类的ID
            $new_category_id = $db->insert_id();
            $id = $new_category_id; // 设置id以便后续使用
            
            // 在post_count表中添加对应的计数记录
            $insert_count_sql = "INSERT INTO post_count (category_id, post_count) VALUES (?, 0)";
            $db->query($insert_count_sql, array($new_category_id));
        }
        
        // 更新父分类的subcategory_ids字段 - 如果有父分类
        if ($parent_id > 0) {
            update_parent_subcategory_ids($parent_id);
        }
        
        // 递归更新所有可能受影响的上级分类的子分类数据
        update_all_parent_subcategory_ids();
        
        // 提交事务
        $db->commit();

        // 清理分类相关缓存
        if (function_exists('clearCategoryCache')) {
            clearCategoryCache();
        }

        return array('success' => true);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("保存分类失败: " . $e->getMessage());
        return array('success' => false, 'error' => '保存分类失败: ' . $e->getMessage());
    }
}

/**
 * 批量添加分类
 */
function batch_add_categories() {
    global $db, $cache;
    
    // 获取提交的数据
    $parent_id = isset($_POST['parent_id']) ? intval($_POST['parent_id']) : 0;
    $categories_text = isset($_POST['categories']) ? trim($_POST['categories']) : '';
    
    // 验证数据
    if (empty($categories_text)) {
        return ['success' => false, 'error' => '分类列表不能为空'];
    }
    
    // 将文本拆分为行
    $lines = explode("\n", str_replace("\r", "", $categories_text));
    $count = 0;
    $now = time();
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            // 生成拼音
            $pinyin = generate_pinyin($line);
            
            // 插入分类数据
            $sql = "INSERT INTO categories (
                    parent_id, name, pinyin, sort_order, status, created_at, updated_at
                ) VALUES (
                    '" . $parent_id . "',
                    '" . $db->escape($line) . "',
                    '" . $db->escape($pinyin) . "',
                    '0',
                    '1',
                    '" . $now . "',
                    '" . $now . "'
                )";
            
            if ($db->query($sql)) {
                // 获取新插入的分类ID
                $category_id = $db->insert_id();
                
                // 在post_count表中添加对应的计数记录
                $insert_count_sql = "INSERT INTO post_count (category_id, post_count) VALUES ($category_id, 0)";
                $db->query($insert_count_sql);
                
                $count++;
            }
        }
        
        // 提交事务
        $db->commit();

        if ($count > 0) {
            // 清理分类相关缓存
            if (function_exists('clearCategoryCache')) {
                clearCategoryCache();
            }

            return ['success' => true, 'count' => $count];
        } else {
            return ['success' => false, 'error' => '批量添加分类失败'];
        }
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("批量添加分类失败: " . $e->getMessage());
        return ['success' => false, 'error' => '批量添加分类失败: ' . $e->getMessage()];
    }
}

/**
 * 删除分类
 */
function delete_category($id) {
    global $db, $cache;
    
    $id = intval($id);
    
    // 检查分类是否存在
    $sql = "SELECT id, parent_id FROM categories WHERE id = ?";
    $result = $db->query($sql, array($id));
    if (!$result || $db->num_rows($result) == 0) {
        return array('success' => false, 'error' => '分类不存在');
    }
    
    $category = $db->fetch_array($result);
    $parent_id = $category['parent_id'];
    
    // 检查是否有子分类
    $children = get_direct_children($id);
    if (!empty($children)) {
        return array('success' => false, 'error' => '该分类下有子分类，不能删除');
    }
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        // 执行删除
        $sql = "DELETE FROM categories WHERE id = ?";
        $result = $db->query($sql, array($id));
        
        if (!$result) {
            throw new Exception("删除分类失败");
        }
        
        // 同时删除post_count表中对应的计数记录
        $sql = "DELETE FROM post_count WHERE category_id = ?";
        $db->query($sql, array($id));
        
        // 如果有父分类，更新父分类的subcategory_ids字段
        if ($parent_id > 0) {
            update_parent_subcategory_ids($parent_id);
        }
        
        // 递归更新所有可能受影响的上级分类的子分类数据
        update_all_parent_subcategory_ids();
        
        // 提交事务
        $db->commit();

        // 清理分类相关缓存
        if (function_exists('clearCategoryCache')) {
            clearCategoryCache();
        }

        return array('success' => true);
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("删除分类失败: " . $e->getMessage());
        return array('success' => false, 'error' => '删除分类失败: ' . $e->getMessage());
    }
}

/**
 * 切换分类状态（启用/禁用）
 */
function toggle_category_status($id) {
    global $db;

    try {
        // 获取当前状态
        $sql = "SELECT status FROM categories WHERE id = ?";
        $result = $db->query($sql, array($id));
        if (!$result || $db->num_rows($result) == 0) {
            return false;
        }

        $row = $db->fetch_array($result);
        $current_status = $row['status'];

        // 切换状态
        $new_status = ($current_status == 1) ? 0 : 1;
        $sql = "UPDATE categories SET status = ? WHERE id = ?";
        $result = $db->query($sql, array($new_status, $id));

        if (!$result) {
            return false;
        }

        return true;
    } catch (Exception $e) {
        error_log("切换分类状态失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取分类的所有子分类ID（递归）
 */
function get_category_children($parent_id) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    $children = [];
    
    // 查找直接子分类
    foreach ($categories as $category) {
        if ($category['parent_id'] == $parent_id) {
            $children[] = $category['id'];
            // 递归查找子分类的子分类
            $sub_children = get_category_children($category['id']);
            $children = array_merge($children, $sub_children);
        }
    }
    
    return $children;
}

/**
 * 获取分类的直接子分类
 */
function get_direct_children($parent_id) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    $children = [];
    
    // 查找直接子分类
    foreach ($categories as $category) {
        if ($category['parent_id'] == $parent_id) {
            $children[] = $category['id'];
        }
    }
    
    return $children;
}

/**
 * 检查分类是否有关联的信息
 */
function has_posts($category_id) {
    global $db;
    
    $sql = "SELECT COUNT(*) as count FROM posts WHERE category_id = " . intval($category_id);
    $result = $db->query($sql);
    
    if ($db->num_rows($result) > 0) {
        $row = $db->fetch_array($result);
        return $row['count'] > 0;
    }
    
    return false;
}

/**
 * 生成分页数据
 */
function generate_pagination($total_items, $current_page, $items_per_page, $show_pages) {
    // 计算总页数
    $total_pages = ceil($total_items / $items_per_page);
    
    // 确保当前页不超过总页数
    $current_page = min($current_page, $total_pages);
    $current_page = max(1, $current_page);
    
    // 计算起始和结束索引
    $start = ($current_page - 1) * $items_per_page + 1;
    $end = min($start + $items_per_page - 1, $total_items);
    
    // 计算显示的页码链接（前后各显示5个页码）
    $start_page = max(1, $current_page - $show_pages);
    $end_page = min($total_pages, $current_page + $show_pages);
    
    // 生成页码链接
    $page_links = [];
    for ($i = $start_page; $i <= $end_page; $i++) {
        $page_links[$i] = '?page=' . $i;
    }
    
    // 拼接分页数据
    $pagination = [
        'total_items' => $total_items,
        'items_per_page' => $items_per_page,
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'start' => $start,
        'end' => $end,
        'page_links' => $page_links,
        'first_link' => '?page=1',
        'last_link' => '?page=' . $total_pages,
        'previous_link' => str_replace('{page}', max(1, $current_page - 1), '?page={page}'),
        'next_link' => str_replace('{page}', min($total_pages, $current_page + 1), '?page={page}')
    ];
    
    return $pagination;
}

/**
 * 设置分页模板变量
 */
function set_pagination_template_vars($pagination, $base_url, $params) {
    global $tpl;
    
    // 处理分页链接
    foreach ($pagination['page_links'] as $page => $link) {
        $pagination['page_links'][$page] = $base_url . 'page=' . $page . $params;
    }
    
    // 添加参数到其他链接
    $pagination['first_link'] = $base_url . 'page=1' . $params;
    $pagination['last_link'] = $base_url . 'page=' . $pagination['total_pages'] . $params;
    $pagination['previous_link'] = $base_url . 'page=' . max(1, $pagination['current_page'] - 1) . $params;
    $pagination['next_link'] = $base_url . 'page=' . min($pagination['total_pages'], $pagination['current_page'] + 1) . $params;
    
    $tpl->assign('pagination', $pagination);
}

/**
 * 获取符合条件的所有分类（不分页）
 */
function get_filtered_categories($parent_id = null, $keyword = '', $only_top_level = false, $only_second_level = false, $status = -1) {
    global $db;

    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();

    // 在内存中过滤分类
    $filtered_categories = array_filter($categories, function($category) use ($parent_id, $only_top_level, $keyword, $only_second_level, $status) {
        if ($parent_id !== null && $category['parent_id'] != $parent_id) {
            return false;
        }
        if ($only_top_level && $category['parent_id'] != 0) {
            return false;
        }
        if ($only_second_level && $category['parent_id'] == 0) {
            return false;
        }
        if (!empty($keyword) && stripos($category['name'], $keyword) === false && stripos($category['pinyin'], $keyword) === false) {
            return false;
        }
        // 状态筛选
        if ($status != -1 && $category['status'] != $status) {
            return false;
        }
        return true;
    });
    
    // 排序过滤后的分类
    usort($filtered_categories, function($a, $b) {
        return $a['id'] - $b['id'];
    });
    
    return $filtered_categories;
}

/**
 * 获取父分类名称
 * @param int $parent_id 父分类ID
 * @return string 父分类名称
 */
function get_parent_name($parent_id) {
    // 使用新的getAllCategories函数获取所有分类数据（包括禁用的）
    $categories = getAllCategories();
    
    // 查找指定ID的分类
    foreach ($categories as $category) {
        if ($category['id'] == $parent_id) {
            return $category['name'];
        }
    }
    
    return '未知分类';
}

/**
 * 获取指定前缀的模板文件列表
 * @param string $prefix 模板文件前缀
 * @return array 模板文件列表
 */
function get_template_files($prefix) {
    $pc_template_dir = dirname(dirname(__FILE__)) . '/template/pc/';
    $mobile_template_dir = dirname(dirname(__FILE__)) . '/template/m/';
    $files = array();
    
    if ($handle = opendir($pc_template_dir)) {
        while (false !== ($file = readdir($handle))) {
            if (is_file($pc_template_dir . $file) && strpos($file, $prefix) === 0 && pathinfo($file, PATHINFO_EXTENSION) === 'htm') {
                // 检查此模板在移动端是否也存在
                $exists_in_mobile = file_exists($mobile_template_dir . $file);
                $files[] = array(
                    'name' => $file,
                    'exists_in_mobile' => $exists_in_mobile
                );
            }
        }
        closedir($handle);
    }
    
    return $files;
}

/**
 * 更新父分类的subcategory_ids字段
 * 
 * @param int $parent_id 父分类ID
 * @return bool 是否更新成功
 */
function update_parent_subcategory_ids($parent_id) {
    global $db;
    
    // 获取所有直接子分类ID
    $direct_children = get_direct_children($parent_id);
    
    // 获取所有孙子分类ID（递归）
    $all_grandchildren = [];
    foreach ($direct_children as $child_id) {
        $sql = "SELECT subcategory_ids FROM categories WHERE id = ?";
        $result = $db->query($sql, [$child_id]);
        if ($result && $row = $db->fetch_array($result)) {
            if (!empty($row['subcategory_ids'])) {
                $all_grandchildren = array_merge(
                    $all_grandchildren, 
                    explode(',', $row['subcategory_ids'])
                );
            }
        }
    }
    
    // 合并所有子孙分类ID
    $all_children = array_merge($direct_children, $all_grandchildren);
    $all_children = array_unique(array_map('intval', $all_children));
    
    // 将子分类ID数组转换为逗号分隔的字符串
    $subcategory_ids = empty($all_children) ? '' : implode(',', $all_children);
    
    // 更新父分类的subcategory_ids字段
    $update_sql = "UPDATE categories SET subcategory_ids = ? WHERE id = ?";
    return $db->query($update_sql, [$subcategory_ids, $parent_id]) !== false;
}

/**
 * 更新所有分类的subcategory_ids字段
 * 从底层分类开始更新，确保数据的一致性
 */
function update_all_parent_subcategory_ids() {
    global $db;
    
    // 获取所有分类，按层级排序（先处理最底层分类）
    $sql = "SELECT id, parent_id FROM categories ORDER BY parent_id DESC, id ASC";
    $result = $db->query($sql);
    
    $updated_parents = [];
    $categories = [];
    
    // 构建分类数据
    while ($row = $db->fetch_array($result)) {
        $categories[] = $row;
    }
    
    // 从底层开始更新
    foreach ($categories as $category) {
        $parent_id = $category['parent_id'];
        
        // 如果有父分类且尚未更新过
        if ($parent_id > 0 && !in_array($parent_id, $updated_parents)) {
            update_parent_subcategory_ids($parent_id);
            $updated_parents[] = $parent_id;
        }
    }
    
    return true;
} 